import dataset
from matplotlib import pyplot as plt
import numpy as np


xs,ys = dataset.get_beans(100)


plt.title("Site-Toxicity Function",fontsize=12)
plt.xlabel("Beans Size")
plt.ylabel("Toxicity")

plt.scatter(xs,ys)

def sigmoid(x):
    return 1/(1+np.exp(-x))


#第一层
#第一个神经元

w11_1= np.random.rand()
b1_1= np.random.rand()
w12_1=  np.random.rand()
b2_1= np.random.rand()

#第二个层
w11_2 = np.random.rand()
w21_2= np.random.rand()
b1_2= np.random.rand()


def forward_propgation(xs):
    z1_1=w11_1*xs+b1_1
    a1_1=sigmoid(z1_1)

    z2_1=w12_1*xs+b2_1
    a2_1=sigmoid(z2_1)

    z1_2=w11_2*a1_1+w21_2*a2_1+b1_2
    a1_2=sigmoid(z1_2)
    return a1_2,a2_1,a1_1,z1_1,z2_1
a1_2,a2_1,a1_1,z1_1,z2_1=forward_propgation(xs)


plt.plot(xs,a1_2)

plt.show()

for _ in range(1000):
    for i in range(100):
        x=xs[i]
        y=ys[i]
        a1_2,a2_1,a1_1,z1_1,z2_1=forward_propgation(x)
        e=(y-a1_2)**2

        deda1_2= -2*(y-a1_2)
        dadz1_2=a1_2*(1-a1_2)
        dz1_2dw11_2=a1_1
        dz1_2dw21_2=a2_1

        dedw11_2=deda1_2*dz1_2dw21_2*dz1_2dw11_2
        dedw21_2=deda1_2*dz1_2dw21_2*dz1_2dw21_2
        dz1_2db1_2=1
        dedb1_2=deda1_2*dadz1_2*dz1_2db1_2

        
        dz1_2db1_1=w11_2
        da1_1dz1_1=a1_1*(1-a1_1)
        dz1_1dw11_1=x
        dedw11_1=deda1_2*da1_1dz1_1*dz1_2dw11_1*da1_1dz1_1



    plt.clf()
    plt.scatter(xs,ys)
    z=w*xs+b
    a=1/(1+np.exp(-z))

    y_pre=w*xs+b
    plt.xlim(0,1)
    plt.ylim(0,1.2)
    plt.plot(xs,a)
    plt.pause(0.01)