import dataset
from matplotlib import pyplot as plt

xs,ys = dataset.get_beans(100)
print(xs,ys)

plt.title("Site-Toxicity Function",fontsize=12)
plt.xlabel("Beans Size")
plt.ylabel("Toxicity")
plt.scatter(xs,ys)

w=0.5

for m in range(100):
    for i in range(100):
        x=xs[i]
        y=ys[i]
        y_pre=w*x
        e=y-y_pre
        alpha=0.05
        w=w+alpha*e*x



print("Final weight:", w)
y_pre=w*xs
print(y_pre)
plt.plot(xs,y_pre)

plt.show()