import dataset
from matplotlib import pyplot as plt
import numpy as np
from mpl_toolkits.mplot3d import Axes3D

m=100
xs,ys = dataset.get_beans(m)

# First plot - 2D scatter plot
plt.figure(1)
plt.title("Site-Toxicity Function",fontsize=12)
plt.xlabel("Beans Size")
plt.ylabel("Toxicity")
plt.xlim(0,1)
plt.ylim(0,1.5)
plt.scatter(xs,ys)

w=0.1
b=0.1

y_pre=w*xs+b
plt.plot(xs,y_pre)  

plt.show()

# Second plot - 3D plot
fig = plt.figure(2)
# 确保正确创建3D轴
ax = fig.add_subplot(111, projection='3d')

ws = np.arange(-1,2,0.1)
bs = np.arange(-2,2,0.1)

for b in bs:
    es = []
    for w in ws:
        y_pre = w*xs+b
        e = np.sum((ys-y_pre)**2)*(1/m)
        es.append(e)
    ax.plot(ws, [b]*len(ws), es, zdir='y')

# 尝试使用不同的方法设置z轴限制
try:
    ax.set_zlim(0, 2)
except AttributeError:
    # 如果上面的方法不存在，尝试这个替代方法
    ax.set_zlim3d(0, 2)

# 添加轴标签
ax.set_xlabel('w')
ax.set_ylabel('b')
ax.set_zlabel('Error')

plt.show()