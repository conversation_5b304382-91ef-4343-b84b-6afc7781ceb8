from keras.datasets import mnist
import numpy as np
from keras.models import Sequential
from keras.layers import Dense
from keras.optimizers import SGD
from keras.utils import to_categorical
import matplotlib.pyplot as plt




(X_train, y_train), (X_test, y_test) = mnist.load_data()

print("X_train shape", str(X_train.shape))
print("y_train shape", str(y_train.shape))
print("X_test shape", str(X_test.shape))
print("y_test shape", str(y_test.shape))

print(X_train[0])
plt.imshow(X_train[0],cmap='gray')
plt.show()

# 重塑数据并归一化
X_train = X_train.reshape(60000, 784).astype('float32') / 255.0
X_test = X_test.reshape(10000, 784).astype('float32') / 255.0

# 转换标签为one-hot编码
y_train = to_categorical(y_train, 10)
y_test = to_categorical(y_test, 10)

model = Sequential()
model.add(Dense(units=256,activation='relu',input_dim=784))
model.add(Dense(units=256,activation='relu'))
model.add(Dense(units=256,activation='relu'))
model.add(Dense(units=10,activation='softmax'))



model.compile(loss='categorical_crossentropy',optimizer=SGD(learning_rate=0.05),metrics=['accuracy'])
model.fit(X_train,y_train,epochs=1200,batch_size=4096,validation_data=(X_test, y_test))

# 评估模型
loss, accuracy = model.evaluate(X_test, y_test, verbose=0)
print(f"Test Loss: {loss:.4f}")
print(f"Test Accuracy: {accuracy:.4f}")

# 进行预测
predictions = model.predict(X_test[:10])
print("前10个测试样本的预测结果:")
for i in range(10):
    predicted_class = np.argmax(predictions[i])
    actual_class = np.argmax(y_test[i])
    print(f"样本 {i}: 预测={predicted_class}, 实际={actual_class}")

print("模型权重:")
print(model.get_weights())