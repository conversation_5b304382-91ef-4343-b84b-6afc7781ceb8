import dataset
from matplotlib import pyplot as plt
import numpy as np


xs,ys = dataset.get_beans(100)
print(xs,ys)

plt.title("Site-Toxicity Function",fontsize=12)
plt.xlabel("Beans Size")
plt.ylabel("Toxicity")
plt.scatter(xs,ys)

w=0.1
y_pre=w*xs
plt.plot(xs,y_pre)

plt.show()

es = (ys- y_pre) ** 2
sum_e = np.sum(es)
sum_e = (1/100)*sum_e
print(sum_e)   

ws=np.arange(0,3,0.1)

es=[]


for w in ws:
    y_pre=w*xs
    e = (1/100)*np.sum((ys-y_pre)**2)
    es.append(e)

plt.title("cost function",fontsize=12)
plt.xlabel("w")
plt.ylabel("e")

plt.plot(ws,es)
plt.show()

w_min = np.sum(xs*ys)/np.sum(xs*xs)
print(w_min)


y_pre=w_min*xs

plt.title("Site-Toxicity Function",fontsize=12)
plt.xlabel("Beans Size")
plt.ylabel("Toxicity")
plt.scatter(xs,ys)

plt.plot(xs,y_pre)

plt.show()